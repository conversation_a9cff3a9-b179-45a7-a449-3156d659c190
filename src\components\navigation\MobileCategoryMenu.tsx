"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiChevronDown, FiChevronRight, FiShoppingBag } from 'react-icons/fi';
import { CategoryTreeMainCategory } from '@/services/api';
import { useCategoryTree } from '@/hooks/useCategoryTree';

interface MobileCategoryMenuProps {
  onLinkClick: () => void;
}

const MobileCategoryMenu: React.FC<MobileCategoryMenuProps> = ({ onLinkClick }) => {
  const { categoryTree, isLoading, error } = useCategoryTree();
  const [expandedCategories, setExpandedCategories] = useState<Set<number>>(new Set());
  const [isShopExpanded, setIsShopExpanded] = useState(false);

  const toggleMainCategory = (categoryId: number) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId);
    } else {
      newExpanded.add(categoryId);
    }
    setExpandedCategories(newExpanded);
  };

  const toggleShopMenu = () => {
    setIsShopExpanded(!isShopExpanded);
  };

  return (
    <div className="border-b border-gray-100">
      {/* Shop Menu Toggle */}
      <button
        onClick={toggleShopMenu}
        className="flex items-center justify-between w-full px-4 py-3 text-gray-700 hover:bg-gray-100 hover:text-main-color transition-all duration-200 group"
      >
        <div className="flex items-center gap-4">
          <FiShoppingBag size={20} className="group-hover:scale-110 transition-transform duration-200" />
          <span className="font-medium">Shop</span>
        </div>
        <FiChevronDown 
          className={`transition-transform duration-200 ${isShopExpanded ? 'rotate-180' : ''}`}
          size={16}
        />
      </button>

      {/* Shop Categories Accordion */}
      {isShopExpanded && (
        <div className="bg-gray-50 border-t border-gray-100">
          {/* Direct Shop Link */}
          <Link
            href="/shop"
            onClick={onLinkClick}
            className="flex items-center gap-3 px-8 py-3 text-sm text-gray-600 hover:text-main-color hover:bg-white transition-all duration-200"
          >
            <FiShoppingBag size={16} />
            <span>All Products</span>
          </Link>

          {/* Loading State */}
          {isLoading && categoryTree.length === 0 && (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-main-color"></div>
              <span className="ml-2 text-sm text-gray-600">Loading...</span>
            </div>
          )}

          {/* Error State */}
          {error && categoryTree.length === 0 && (
            <div className="px-8 py-3 text-sm text-red-600">
              {error}
            </div>
          )}

          {/* Categories */}
          {categoryTree.map((mainCategory) => (
            <div key={mainCategory.id} className="border-b border-gray-100 last:border-b-0">
              {/* Main Category */}
              <div className="flex items-center">
                <Link
                  href={`/shop/${mainCategory.slug}`}
                  onClick={onLinkClick}
                  className="flex-1 flex items-center gap-3 px-8 py-3 text-sm text-gray-700 hover:text-main-color hover:bg-white transition-all duration-200"
                >
                  {mainCategory.imageUrl && (
                    <div className="w-6 h-6 rounded overflow-hidden flex-shrink-0">
                      <Image
                        src={mainCategory.imageUrl}
                        alt={mainCategory.name}
                        width={24}
                        height={24}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <span className="font-medium">{mainCategory.name}</span>
                  {mainCategory.count && (
                    <span className="text-xs text-gray-400">({mainCategory.count})</span>
                  )}
                </Link>
                
                {/* Expand/Collapse Button for Subcategories */}
                {mainCategory.subcategories && mainCategory.subcategories.length > 0 && (
                  <button
                    onClick={() => toggleMainCategory(mainCategory.id)}
                    className="p-2 text-gray-400 hover:text-main-color transition-colors duration-200"
                  >
                    <FiChevronRight 
                      className={`transition-transform duration-200 ${
                        expandedCategories.has(mainCategory.id) ? 'rotate-90' : ''
                      }`}
                      size={14}
                    />
                  </button>
                )}
              </div>

              {/* Subcategories */}
              {expandedCategories.has(mainCategory.id) && 
               mainCategory.subcategories && 
               mainCategory.subcategories.length > 0 && (
                <div className="bg-white">
                  {mainCategory.subcategories.map((subcategory) => (
                    <Link
                      key={subcategory.id}
                      href={`/shop/${mainCategory.slug}/${subcategory.slug}`}
                      onClick={onLinkClick}
                      className="flex items-center gap-3 px-12 py-2 text-sm text-gray-600 hover:text-main-color hover:bg-gray-50 transition-all duration-200"
                    >
                      <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                      <span>{subcategory.name}</span>
                      {subcategory.count && (
                        <span className="text-xs text-gray-400 ml-auto">({subcategory.count})</span>
                      )}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default MobileCategoryMenu;
