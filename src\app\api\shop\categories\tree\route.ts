import { NextRequest, NextResponse } from 'next/server';

// Import environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

// GET handler for fetching the complete category tree
export async function GET() {
  try {
    // Construct the backend URL for the category tree
    const backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/categories/tree`;
    
    console.log(`Category Tree API proxy - Calling backend URL: ${backendUrl}`);
    
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      // Add cache control for ISR
      next: { revalidate: 7200 } // 2 hours cache
    });
    
    if (!response.ok) {
      console.error(`Category Tree API proxy - Error response: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch category tree from backend' },
        { status: response.status }
      );
    }
    
    let data;
    try {
      data = await response.json();
      console.log('Category Tree API response:', data);
    } catch (e) {
      console.error('Error parsing JSON response for category tree:', e);
      // Return empty data structure if we can't parse the response
      return NextResponse.json({ data: [] });
    }
    
    // Return the data with proper structure
    return NextResponse.json(data);
  } catch (error) {
    console.error('API proxy error (GET /categories/tree):', error);
    return NextResponse.json(
      { error: 'Failed to fetch category tree' },
      { status: 500 }
    );
  }
}
