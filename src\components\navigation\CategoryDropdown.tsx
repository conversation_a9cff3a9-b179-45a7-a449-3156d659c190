"use client";

import React, { useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiChevronRight, FiShoppingBag } from 'react-icons/fi';
import { CategoryTreeMainCategory } from '@/services/api';
import { useCategoryTree, preloadCategoryTree } from '@/hooks/useCategoryTree';

interface CategoryDropdownProps {
  isVisible: boolean;
  onClose: () => void;
}

const CategoryDropdown: React.FC<CategoryDropdownProps> = ({ isVisible, onClose }) => {
  const { categoryTree, isLoading, error } = useCategoryTree();

  // Preload category tree on component mount for better performance
  useEffect(() => {
    preloadCategoryTree();
  }, []);

  if (!isVisible) return null;

  return (
    <div
      className={`absolute top-full left-0 w-screen max-w-6xl bg-white shadow-2xl border border-gray-100 rounded-lg z-[200] mt-2 hidden lg:block transition-all duration-300 ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2 pointer-events-none'
      }`}
      role="menu"
      aria-label="Shop categories menu"
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-main-color to-main-color/90 text-white p-4 rounded-t-lg">
        <div className="flex items-center gap-2">
          <FiShoppingBag size={20} />
          <h3 className="text-lg font-semibold">Shop by Category</h3>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-main-color"></div>
            <span className="ml-3 text-gray-600">Loading categories...</span>
          </div>
        )}

        {error && (
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="text-main-color hover:underline"
            >
              Try again
            </button>
          </div>
        )}

        {!isLoading && !error && categoryTree.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-600">No categories available</p>
          </div>
        )}

        {!isLoading && !error && categoryTree.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categoryTree.map((mainCategory) => (
              <div key={mainCategory.id} className="group">
                {/* Main Category Header */}
                <Link
                  href={`/shop/${mainCategory.slug}`}
                  onClick={onClose}
                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 mb-2"
                  role="menuitem"
                  aria-label={`Browse ${mainCategory.name} products`}
                >
                  {mainCategory.imageUrl && (
                    <div className="w-12 h-12 rounded-lg overflow-hidden flex-shrink-0">
                      <Image
                        src={mainCategory.imageUrl}
                        alt={mainCategory.name}
                        width={48}
                        height={48}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                    </div>
                  )}
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 group-hover:text-main-color transition-colors duration-200">
                      {mainCategory.name}
                    </h4>
                    {mainCategory.count && (
                      <p className="text-sm text-gray-500">{mainCategory.count} products</p>
                    )}
                  </div>
                  <FiChevronRight className="text-gray-400 group-hover:text-main-color transition-colors duration-200" />
                </Link>

                {/* Subcategories */}
                {mainCategory.subcategories && mainCategory.subcategories.length > 0 && (
                  <div className="ml-4 space-y-1">
                    {mainCategory.subcategories.slice(0, 5).map((subcategory) => (
                      <Link
                        key={subcategory.id}
                        href={`/shop/${mainCategory.slug}/${subcategory.slug}`}
                        onClick={onClose}
                        className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-main-color hover:bg-main-color/5 rounded-md transition-all duration-200 group"
                        role="menuitem"
                        aria-label={`Browse ${subcategory.name} in ${mainCategory.name}`}
                      >
                        <div className="w-1 h-1 bg-gray-400 rounded-full group-hover:bg-main-color transition-colors duration-200"></div>
                        <span className="flex-1">{subcategory.name}</span>
                        {subcategory.count && (
                          <span className="text-xs text-gray-400">({subcategory.count})</span>
                        )}
                      </Link>
                    ))}
                    
                    {/* Show more link if there are more than 5 subcategories */}
                    {mainCategory.subcategories.length > 5 && (
                      <Link
                        href={`/shop/${mainCategory.slug}`}
                        onClick={onClose}
                        className="flex items-center gap-2 px-3 py-2 text-sm text-main-color hover:bg-main-color/5 rounded-md transition-all duration-200"
                      >
                        <span>View all {mainCategory.subcategories.length} categories</span>
                        <FiChevronRight size={14} />
                      </Link>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Footer */}
        <div className="mt-6 pt-4 border-t border-gray-100">
          <Link
            href="/shop"
            onClick={onClose}
            className="inline-flex items-center gap-2 text-main-color hover:text-main-color/80 font-medium transition-colors duration-200"
          >
            <FiShoppingBag size={16} />
            <span>View All Products</span>
            <FiChevronRight size={16} />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default CategoryDropdown;
