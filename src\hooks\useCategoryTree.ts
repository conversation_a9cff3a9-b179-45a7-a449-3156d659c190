"use client";

import { useState, useEffect, useCallback } from 'react';
import { categoryTreeApi, CategoryTreeResponse, CategoryTreeMainCategory } from '@/services/api';

interface UseCategoryTreeReturn {
  categoryTree: CategoryTreeMainCategory[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Global cache for category tree data
let globalCategoryTree: CategoryTreeMainCategory[] | null = null;
let globalCacheTimestamp: number | null = null;
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes in milliseconds

// Check if cache is still valid
const isCacheValid = (): boolean => {
  if (!globalCacheTimestamp) return false;
  return Date.now() - globalCacheTimestamp < CACHE_DURATION;
};

export const useCategoryTree = (): UseCategoryTreeReturn => {
  const [categoryTree, setCategoryTree] = useState<CategoryTreeMainCategory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategoryTree = useCallback(async () => {
    // Check if we have valid cached data
    if (globalCategoryTree && isCacheValid()) {
      setCategoryTree(globalCategoryTree);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response: CategoryTreeResponse = await categoryTreeApi.getTree();
      const data = response.data || [];
      
      // Update global cache
      globalCategoryTree = data;
      globalCacheTimestamp = Date.now();
      
      setCategoryTree(data);
    } catch (err) {
      console.error('Error fetching category tree:', err);
      setError('Failed to load categories');
      
      // If we have stale cached data, use it as fallback
      if (globalCategoryTree) {
        setCategoryTree(globalCategoryTree);
      }
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    // Force refresh by clearing cache
    globalCategoryTree = null;
    globalCacheTimestamp = null;
    await fetchCategoryTree();
  }, [fetchCategoryTree]);

  useEffect(() => {
    fetchCategoryTree();
  }, [fetchCategoryTree]);

  return {
    categoryTree,
    isLoading,
    error,
    refetch,
  };
};

// Preload function to fetch category tree in advance
export const preloadCategoryTree = async (): Promise<void> => {
  if (globalCategoryTree && isCacheValid()) {
    return; // Already cached and valid
  }

  try {
    const response: CategoryTreeResponse = await categoryTreeApi.getTree();
    globalCategoryTree = response.data || [];
    globalCacheTimestamp = Date.now();
  } catch (error) {
    console.error('Error preloading category tree:', error);
  }
};

// Clear cache function (useful for admin operations)
export const clearCategoryTreeCache = (): void => {
  globalCategoryTree = null;
  globalCacheTimestamp = null;
};
