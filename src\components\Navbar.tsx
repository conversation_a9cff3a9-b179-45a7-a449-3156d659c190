"use client";

import Link from "next/link";
import Menu from "./Menu";
import Image from "next/image";
import SearchBar from "./SearchBar";
import dynamic from "next/dynamic";
import { useState } from "react";
import CategoryDropdown from "./navigation/CategoryDropdown";
import { preloadCategoryTree } from "@/hooks/useCategoryTree";

const NavIcons = dynamic(() => import("./NavIcons"), { ssr: false });

const Navbar = () => {
  const [isShopDropdownVisible, setIsShopDropdownVisible] = useState(false);

  const navigationLinks = [
    { href: "/", label: "Home" },
    { href: "/shop", label: "Shop" },
    { href: "/wholesale", label: "Wholesale" },
    { href: "/blog", label: "Blog" },
    { href: "/about", label: "About" },
    { href: "/contact", label: "Contact" },
  ];

  return (
    <header className="sticky top-0 z-[100] bg-white/95 backdrop-blur-md border-b border-gray-100 shadow-sm">
      <div className="h-20 px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 relative">
        {/* MOBILE */}
        <div className="h-full flex items-center justify-between md:hidden">
          <Link href="/" className="flex items-center gap-2">
            <Image
              src="/images/cocojojo.png"
              alt="CocoJojo"
              width={40}
              height={40}
              className="rounded-lg"
            />
            <span className="text-xl font-bold text-gray-900 tracking-wide">CocoJojo</span>
          </Link>
          <Menu />
        </div>

        {/* DESKTOP */}
        <div className="hidden md:flex items-center justify-between gap-8 h-full">
          {/* LEFT - Logo and Navigation */}
          <div className="flex items-center gap-8 lg:gap-12">
            <Link href="/" className="flex items-center gap-3 group">
              <Image
                src="/images/cocojojo.png"
                alt="CocoJojo"
                width={50}
                height={50}
                className="rounded-lg group-hover:scale-105 transition-transform duration-200"
              />
              <span className="text-xl font-bold text-gray-900 tracking-wide group-hover:text-main-color transition-colors duration-200">
                CocoJojo
              </span>
            </Link>

            <nav className="hidden lg:flex items-center gap-1">
              {navigationLinks.map((link) => {
                if (link.label === "Shop") {
                  return (
                    <div
                      key={link.href}
                      className="relative"
                      onMouseEnter={() => {
                        setIsShopDropdownVisible(true);
                        preloadCategoryTree(); // Preload data on hover
                      }}
                      onMouseLeave={() => setIsShopDropdownVisible(false)}
                    >
                      <Link
                        href={link.href}
                        className="px-4 py-2 text-gray-700 hover:text-main-color hover:bg-main-color/5 rounded-lg transition-all duration-200 font-medium"
                      >
                        {link.label}
                      </Link>
                      <CategoryDropdown
                        isVisible={isShopDropdownVisible}
                        onClose={() => setIsShopDropdownVisible(false)}
                      />
                    </div>
                  );
                }

                return (
                  <Link
                    key={link.href}
                    href={link.href}
                    className="px-4 py-2 text-gray-700 hover:text-main-color hover:bg-main-color/5 rounded-lg transition-all duration-200 font-medium"
                  >
                    {link.label}
                  </Link>
                );
              })}
            </nav>
          </div>

          {/* RIGHT - Search and Icons */}
          <div className="flex items-center gap-4 lg:gap-6">
            <div className="hidden lg:block flex-1 max-w-md">
              <SearchBar />
            </div>
            <NavIcons />
          </div>
        </div>

        {/* Mobile Search Bar */}
        <div className="md:hidden absolute top-full left-0 right-0 bg-white border-b border-gray-100 px-4 py-3 lg:hidden">
          <SearchBar />
        </div>
      </div>
    </header>
  );
};

export default Navbar;